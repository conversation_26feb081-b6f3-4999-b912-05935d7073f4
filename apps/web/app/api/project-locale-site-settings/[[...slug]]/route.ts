import { NextRequest } from "next/server"
import { hasProjectAccess, authUser } from "@repo/auth/server"
import { prisma } from "@repo/db"
import { R } from "@repo/utils/server"
import { AutoRouter } from "itty-router"
import { getLogger } from "@repo/logger"
import { translateProjectLocaleSetting } from "./translate"
import {
	queryParamsSchema,
	getSettingSchema,
	upsertSettingSchema,
	translateSettingSchema,
} from "./schemas"
import { createTranslationTask } from "@/lib/services/BackgroundTaskService"
import { ProjectLocaleSiteSettingType } from "@repo/shared-types"

const logger = getLogger("ProjectLocaleSiteSettings")

// 创建路由处理器
const router = AutoRouter({ base: "/api/project-locale-site-settings" })

// 获取项目设置
router.get("/all", async (request) => {
	try {
		const { projectId, type, locale } = queryParamsSchema.parse(request.query)

		// 验证项目访问权限
		await hasProjectAccess(projectId)

		// 构建查询条件
		const where: any = { projectId }
		if (type) where.type = type
		if (locale) where.locale = locale

		// 查询设置
		const settings = await prisma.projectLocaleSiteSetting.findMany({
			where,
			orderBy: { updatedAt: "desc" },
		})

		return R.ok(settings)
	} catch (error) {
		logger.error("获取项目设置时出错:", error)
		const errorMessage = error instanceof Error ? error.message : String(error)
		return R.error(errorMessage)
	}
})

// 获取单个设置
router.get("/", async (request) => {
	try {
		const { projectId, type, locale } = getSettingSchema.parse(request.query)

		// 验证项目访问权限
		await hasProjectAccess(projectId)

		// 查询设置
		const setting = await prisma.projectLocaleSiteSetting.findFirst({
			where: {
				projectId,
				type,
				locale,
			},
		})

		if (!setting) {
			return R.ok({ content: null })
		}

		return R.ok(setting)
	} catch (error) {
		logger.error("获取单个设置时出错:", error)
		const errorMessage = error instanceof Error ? error.message : String(error)
		return R.error(errorMessage)
	}
})

// 创建或更新设置
router.post("/", async (request) => {
	try {
		const body = await request.json()
		const { projectId, locale, type, content, text, status } =
			upsertSettingSchema.parse(body)

		// 验证项目访问权限
		await hasProjectAccess(projectId)

		// 获取当前用户
		const user = await authUser()
		if (!user) {
			return R.error("用户未登录", { status: 401 })
		}

		// 创建或更新设置
		const result = await prisma.projectLocaleSiteSetting.upsert({
			where: {
				project_locale_site_setting_unique: {
					projectId,
					locale,
					type,
				},
			},
			update: {
				content,
				text,
				status,
				updatedAt: new Date(),
			},
			create: {
				projectId,
				locale,
				type,
				content,
				text,
				status,
			},
		})


		// 检查是否需要创建翻译任务
		const { shouldTranslate, targetLocales } = await shouldCreateProjectSettingTranslationTask(
			projectId,
			type,
			locale,
			content,
			result.id // 传入设置ID用于检查是否有变化
		)

		if (shouldTranslate && targetLocales.length > 0) {
			// 创建翻译任务
			await createTranslationTask(`${process.env.TASK_QUEUE_NAME}_translation_tasks`,`ProjectSiteSetting_${type}`,projectId, user.id, {
				contentType: type,
				contentId: result.id,
				sourceLocale: locale,
				targetLocales,
				fieldsToTranslate: getFieldsToTranslate(type),
				extraParams: {
					settingId: result.id
				}
			})
		}
		// 如果不需要翻译，则


		return R.ok({
			...result
		})
	} catch (error) {
		logger.error("保存设置时出错:", error)
		const errorMessage = error instanceof Error ? error.message : String(error)
		return R.error(errorMessage)
	}
})

/**
 * 根据设置类型获取需要翻译的字段
 */
function getFieldsToTranslate(type: string): string[] {
	switch (type) {
		case ProjectLocaleSiteSettingType.Metadata:
			return ["title", "description"]
		case ProjectLocaleSiteSettingType.Nav:
			return ["label"]
		case ProjectLocaleSiteSettingType.GameCategories:
		case ProjectLocaleSiteSettingType.ArticleCategories:
			return ["name", "metadata.title", "metadata.description"]
		default:
			return []
	}
}

/**
 * 判断项目设置是否需要创建翻译任务
 */
async function shouldCreateProjectSettingTranslationTask(
	projectId: string,
	type: string,
	locale: string,
	newContent: any,
	existingSettingId?: string
): Promise<{ shouldTranslate: boolean; targetLocales: string[] }> {
	// 获取项目的语言设置
	const siteSettings = await prisma.projectSiteSetting.findUnique({
		where: { projectId },
		select: {
			languanges: true,
			defaultLocale: true
		}
	})
	if (!siteSettings) {
		logger.error("Project site settings not found", { projectId })
		throw new Error("Project site settings not found")
	}

	const languages = siteSettings.languanges as string[]
	const defaultLanguage = siteSettings.defaultLocale

	if (!languages || !Array.isArray(languages) || languages.length <= 1) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果当前语言不是默认语言，不创建翻译任务
	if (locale !== defaultLanguage) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 获取需要翻译的目标语言
	const targetLocales = languages.filter(lang => lang !== defaultLanguage)
	if (targetLocales.length === 0) {
		return { shouldTranslate: false, targetLocales: [] }
	}

	// 如果是更新操作，检查内容是否有变化
	if (existingSettingId) {
		const existingSetting = await prisma.projectLocaleSiteSetting.findUnique({
			where: { id: existingSettingId },
			select: { content: true }
		})

		if (existingSetting) {
			// 检查需要翻译的字段是否有变化
			const fieldsToTranslate = getFieldsToTranslate(type)
			const hasChanges = fieldsToTranslate.some(field => {
				const oldValue = getNestedValue(existingSetting.content, field)
				const newValue = getNestedValue(newContent, field)
				return oldValue !== newValue
			})
			logger.info(`当前网站设置,type: ${type} 有变化: ${hasChanges},需要翻译的目标语言: ${targetLocales}`)
			if (!hasChanges) {
				return { shouldTranslate: false, targetLocales: [] }
			}
		}
	}

	// 检查其他语言的内容是否为空
	const existingTranslations = await prisma.projectLocaleSiteSetting.findMany({
		where: {
			projectId,
			type,
			locale: { in: targetLocales }
		},
		select: { locale: true, content: true }
	})

	// 找出需要翻译的语言（内容为空或不存在的语言）
	const localesNeedingTranslation = targetLocales.filter(targetLocale => {
		const existingTranslation = existingTranslations.find(t => t.locale === targetLocale)
		if (!existingTranslation) return true

		// 检查是否有任何需要翻译的字段为空
		const fieldsToTranslate = getFieldsToTranslate(type)
		return fieldsToTranslate.some(field => {
			const value = getNestedValue(existingTranslation.content, field)
			return !value || (typeof value === 'string' && value.trim() === '')
		})
	})
	logger.info(`当前网站设置,type: ${type} 存在缺失翻译的目标语言: ${localesNeedingTranslation} ，需要异步创建翻译任务`)
	return {
		shouldTranslate: localesNeedingTranslation.length > 0,
		targetLocales: localesNeedingTranslation
	}
}

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: any, path: string): any {
	if (!obj || typeof obj !== 'object') return undefined

	return path.split('.').reduce((current, key) => {
		return current && current[key]
	}, obj)
}


// 404处理
router.all("*", () => {
	return Response.json(
		{
			success: false,
			code: 404,
			message: "API endpoint not found",
		},
		{ status: 404 },
	)
})

export const GET = async (request: NextRequest) => router.fetch(request)
export const POST = async (request: NextRequest) => router.fetch(request)
export const PUT = async (request: NextRequest) => router.fetch(request)
export const DELETE = async (request: NextRequest) => router.fetch(request)
